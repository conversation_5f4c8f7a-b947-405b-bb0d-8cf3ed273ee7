# Docker Setup for Portfolio Pro

This document provides instructions for running the Portfolio Pro application using Docker.

## Prerequisites

- Docker installed on your system
- Docker Compose (usually included with Docker Desktop)

## Quick Start

### Production Build

1. **Build and run the production container:**
   ```bash
   docker-compose up --build
   ```

2. **Access the application:**
   Open your browser and navigate to `http://localhost:3000`

### Development Mode

1. **Run in development mode with hot reloading:**
   ```bash
   docker-compose --profile dev up --build portfolio-pro-dev
   ```

2. **Access the development server:**
   Open your browser and navigate to `http://localhost:3001`

## Docker Commands

### Building the Image

```bash
# Build production image
docker build -t portfolio-pro .

# Build development image
docker build -f Dockerfile.dev -t portfolio-pro-dev .
```

### Running Containers

```bash
# Run production container
docker run -p 3000:3000 portfolio-pro

# Run development container with volume mounting
docker run -p 3000:3000 -v $(pwd):/app -v /app/node_modules portfolio-pro-dev
```

### Using Docker Compose

```bash
# Start production services
docker-compose up

# Start in background
docker-compose up -d

# Start development services
docker-compose --profile dev up

# Stop services
docker-compose down

# Rebuild and start
docker-compose up --build

# View logs
docker-compose logs -f
```

## Environment Variables

The following environment variables can be configured:

- `NODE_ENV`: Set to `production` or `development`
- `NEXT_TELEMETRY_DISABLED`: Set to `1` to disable Next.js telemetry
- `PORT`: Port number (default: 3000)
- `HOSTNAME`: Hostname (default: 0.0.0.0)

### Setting Environment Variables

Create a `.env` file in the project root:

```env
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
# Add your custom environment variables here
```

## Multi-Stage Build Explanation

The production Dockerfile uses a multi-stage build process:

1. **Base**: Sets up the Node.js environment
2. **Dependencies**: Installs npm/pnpm dependencies with caching
3. **Builder**: Builds the Next.js application
4. **Runner**: Creates the final minimal production image

This approach results in:
- Smaller final image size
- Better security (no build tools in production)
- Faster subsequent builds through layer caching

## Optimization Features

- **Layer caching**: Dependencies are cached separately from source code
- **Non-root user**: Application runs as `nextjs` user for security
- **Standalone output**: Next.js standalone mode for minimal runtime
- **Alpine Linux**: Smaller base image
- **Health checks**: Built-in health monitoring

## Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   # Check what's using port 3000
   lsof -i :3000
   
   # Use a different port
   docker run -p 3001:3000 portfolio-pro
   ```

2. **Permission issues:**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   ```

3. **Build failures:**
   ```bash
   # Clean Docker cache
   docker system prune -a
   
   # Rebuild without cache
   docker-compose build --no-cache
   ```

### Logs and Debugging

```bash
# View container logs
docker-compose logs portfolio-pro

# Follow logs in real-time
docker-compose logs -f portfolio-pro

# Execute commands in running container
docker-compose exec portfolio-pro sh

# Inspect container
docker inspect portfolio-pro
```

## Performance Tips

1. **Use .dockerignore**: Exclude unnecessary files from build context
2. **Multi-stage builds**: Keep production images minimal
3. **Layer caching**: Order Dockerfile commands for optimal caching
4. **Health checks**: Monitor application health
5. **Resource limits**: Set appropriate CPU/memory limits in production

## Production Deployment

For production deployment, consider:

1. **Environment variables**: Use proper environment configuration
2. **Reverse proxy**: Use Nginx or similar for SSL termination
3. **Monitoring**: Implement logging and monitoring solutions
4. **Scaling**: Use Docker Swarm or Kubernetes for scaling
5. **Security**: Regular security updates and vulnerability scanning

## Next Steps

- Configure CI/CD pipeline for automated builds
- Set up monitoring and logging
- Implement backup strategies
- Configure SSL certificates
- Set up load balancing for high availability
