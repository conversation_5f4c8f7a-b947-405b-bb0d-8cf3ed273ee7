Stack trace:
Frame         Function      Args
0007FFFFB750  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB750, 0007FFFFA650) msys-2.0.dll+0x1FE8E
0007FFFFB750  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA28) msys-2.0.dll+0x67F9
0007FFFFB750  000210046832 (000210286019, 0007FFFFB608, 0007FFFFB750, 000000000000) msys-2.0.dll+0x6832
0007FFFFB750  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB750  000210068E24 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA30  00021006A225 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9AEEA0000 ntdll.dll
7FF9AE2A0000 KERNEL32.DLL
7FF9AC500000 KERNELBASE.dll
7FF9AE980000 USER32.dll
000210040000 msys-2.0.dll
7FF9ACBA0000 win32u.dll
7FF9AE270000 GDI32.dll
7FF9AC0F0000 gdi32full.dll
7FF9AC2D0000 msvcp_win.dll
7FF9ACA50000 ucrtbase.dll
7FF9AE380000 advapi32.dll
7FF9ACC60000 msvcrt.dll
7FF9AE440000 sechost.dll
7FF9ADDD0000 RPCRT4.dll
7FF9AB530000 CRYPTBASE.DLL
7FF9AC230000 bcryptPrimitives.dll
7FF9AECC0000 IMM32.DLL
