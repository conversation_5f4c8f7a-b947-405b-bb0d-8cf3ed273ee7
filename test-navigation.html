<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .status {
            font-weight: bold;
        }
        .pass { color: #28a745; }
        .fail { color: #dc3545; }
        .info { color: #17a2b8; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Portfolio Navigation Test Suite</h1>
        <p>This page helps verify that all navigation functionality is working correctly.</p>
        
        <div class="test-section">
            <h3>🔗 Navigation Links Test</h3>
            <p>Click each button to test navigation to different sections:</p>
            <button onclick="testNavigation('home')">Test Home</button>
            <button onclick="testNavigation('services')">Test Services</button>
            <button onclick="testNavigation('stats')">Test Stats</button>
            <button onclick="testNavigation('projects')">Test Projects</button>
            <button onclick="testNavigation('testimonials')">Test Testimonials</button>
            <button onclick="testNavigation('contact')">Test Contact</button>
            <div id="nav-results"></div>
        </div>

        <div class="test-section">
            <h3>📱 Mobile Navigation Test</h3>
            <p>Instructions for manual testing:</p>
            <div class="test-item">
                <strong>1. Resize browser to mobile width (&lt; 768px)</strong>
                <div class="status info">Check that hamburger menu appears</div>
            </div>
            <div class="test-item">
                <strong>2. Click hamburger menu</strong>
                <div class="status info">Verify mobile menu opens with all navigation items</div>
            </div>
            <div class="test-item">
                <strong>3. Click navigation items</strong>
                <div class="status info">Ensure smooth scrolling works and menu closes</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🌙 Theme Toggle Test</h3>
            <p>Instructions for manual testing:</p>
            <div class="test-item">
                <strong>1. Click theme toggle (sun/moon icon)</strong>
                <div class="status info">Verify smooth transition between light/dark themes</div>
            </div>
            <div class="test-item">
                <strong>2. Navigate between sections</strong>
                <div class="status info">Ensure theme persists during navigation</div>
            </div>
            <div class="test-item">
                <strong>3. Refresh page</strong>
                <div class="status info">Confirm theme preference is saved</div>
            </div>
        </div>

        <div class="test-section">
            <h3>✨ Smooth Scrolling Test</h3>
            <p>Instructions for manual testing:</p>
            <div class="test-item">
                <strong>1. Click navigation links</strong>
                <div class="status info">Verify Lenis.js smooth scrolling animation</div>
            </div>
            <div class="test-item">
                <strong>2. Check scroll duration</strong>
                <div class="status info">Should take ~1.5 seconds with easing</div>
            </div>
            <div class="test-item">
                <strong>3. Test active states</strong>
                <div class="status info">Active section should highlight in navigation</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 Active State Test</h3>
            <button onclick="testActiveStates()">Check Active States</button>
            <div id="active-results"></div>
        </div>

        <div class="test-section">
            <h3>📊 Test Results Summary</h3>
            <div id="summary-results">
                <div class="test-item">
                    <div class="status info">Run tests above to see results</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testNavigation(sectionId) {
            const resultsDiv = document.getElementById('nav-results');
            
            // Open the main portfolio page in a new tab
            const portfolioUrl = 'http://localhost:3000';
            const newWindow = window.open(portfolioUrl, '_blank');
            
            // Wait for page to load, then test navigation
            setTimeout(() => {
                try {
                    // Try to navigate to the section
                    newWindow.location.hash = sectionId;
                    
                    resultsDiv.innerHTML += `
                        <div class="test-item">
                            <strong>Testing navigation to #${sectionId}</strong>
                            <div class="status pass">✓ Navigation link opened in new tab</div>
                            <div class="status info">Manually verify smooth scroll and active state</div>
                        </div>
                    `;
                } catch (error) {
                    resultsDiv.innerHTML += `
                        <div class="test-item">
                            <strong>Testing navigation to #${sectionId}</strong>
                            <div class="status fail">✗ Error: ${error.message}</div>
                        </div>
                    `;
                }
            }, 1000);
        }

        function testActiveStates() {
            const resultsDiv = document.getElementById('active-results');
            const expectedSections = ['home', 'services', 'stats', 'projects', 'testimonials', 'contact'];
            
            resultsDiv.innerHTML = '<div class="test-item"><strong>Expected Sections:</strong></div>';
            
            expectedSections.forEach(section => {
                resultsDiv.innerHTML += `
                    <div class="test-item">
                        <strong>Section: #${section}</strong>
                        <div class="status info">Should be trackable by scroll detection</div>
                    </div>
                `;
            });
        }

        // Auto-run some tests on page load
        window.onload = function() {
            document.getElementById('summary-results').innerHTML = `
                <div class="test-item">
                    <div class="status pass">✓ Test page loaded successfully</div>
                    <div class="status info">Portfolio should be running at http://localhost:3000</div>
                    <div class="status info">All navigation sections have been configured with proper IDs</div>
                </div>
            `;
        };
    </script>
</body>
</html>
